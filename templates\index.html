<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>梦羽AI绘图</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            padding-top: 20px;
            background-color: #f8f9fa;
        }
        .card {
            margin-bottom: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .preview-container {
            min-height: 300px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #eee;
            border-radius: 5px;
            overflow: hidden;
        }
        .preview-image {
            max-width: 100%;
            max-height: 600px;
            display: none;
        }
        .loading {
            display: none;
            text-align: center;
        }
        .spinner-border {
            width: 3rem;
            height: 3rem;
        }
        .log-container {
            height: 280px;
            overflow-y: auto;
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
            border: 1px solid #ddd;
            font-family: monospace;
        }
        #logText p {
            margin: 0;
            padding: 2px 0;
        }

        /* 兑换码管理样式 */
        .redemption-stats-card {
            transition: transform 0.2s ease-in-out;
        }

        .redemption-stats-card:hover {
            transform: translateY(-2px);
        }

        .code-item {
            transition: background-color 0.2s ease-in-out;
        }

        .code-item:hover {
            background-color: #f8f9fa;
        }

        .badge {
            font-size: 0.75em;
        }

        .dropdown-toggle::after {
            display: none;
        }

        .toast {
            min-width: 300px;
        }

        /* 兑换码按钮样式 */
        #showRedemptionModalBtn {
            transition: all 0.3s ease;
            border-radius: 20px;
            padding: 8px 16px;
        }

        #showRedemptionModalBtn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        /* 兑换码模态框样式 */
        #modalRedemptionCodeInput {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            transition: border-color 0.3s ease;
        }

        #modalRedemptionCodeInput:focus {
            border-color: #198754;
            box-shadow: 0 0 0 0.2rem rgba(25, 135, 84, 0.25);
        }

        /* 响应式调整 */
        @media (max-width: 768px) {
            .redemption-stats-card .card-body {
                padding: 1rem 0.5rem;
            }

            .code-item .row > div {
                margin-bottom: 0.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 用户信息栏 -->
        <div class="row mb-3">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <div id="userSection">
                            {% if current_user %}
                            <!-- 已登录用户显示 -->
                            <div id="loggedInSection">
                                <div class="row align-items-center mb-3">
                                    <div class="col-md-6">
                                        <h5 class="mb-0">欢迎，{{ current_user.username }}！</h5>
                                        <small class="text-muted">当前积分：<span id="userPoints">{{ current_user.points }}</span> 点</small>
                                    </div>
                                    <div class="col-md-6 text-end">
                                        <a href="/messages" class="btn btn-outline-info btn-sm me-2 position-relative">
                                            <i class="fas fa-envelope me-1"></i>站内信
                                            <span id="unreadBadge" class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger d-none">
                                                <span id="unreadCount">0</span>
                                            </span>
                                        </a>
                                        <button id="logoutBtn" class="btn btn-outline-secondary btn-sm">退出登录</button>
                                        {% if current_user.is_admin %}
                                        <a href="/admin" class="btn btn-outline-primary btn-sm ms-2">管理面板</a>
                                        {% endif %}
                                    </div>
                                </div>
                                <!-- 兑换码和签到按钮区域 -->
                                <div class="row" id="redemptionSection">
                                    <div class="col-md-12">
                                        <a href="/redeem" class="btn btn-outline-success btn-sm me-2">
                                            <i class="fas fa-gift me-1"></i>兑换码
                                        </a>
                                        <a href="/daily_checkin" id="checkinBtn" class="btn btn-outline-primary btn-sm me-2">
                                            <i class="fas fa-calendar-check me-1"></i>每日签到
                                        </a>
                                        <a href="/apply" class="btn btn-outline-warning btn-sm">
                                            <i class="fas fa-paper-plane me-1"></i>申请点数或反馈
                                        </a>
                                        <a href="/imagelist" class="btn btn-outline-info btn-sm">
                                            <i class="fas fa-images me-1"></i>画廊
                                        </a>
                                        <small class="text-muted ms-2 d-block mt-1">
                                            兑换码页面 | 每日签到送积分 | 申请点数或反馈 | 查看画廊
                                            {% if current_user.get('is_admin') or current_user.get('is_authorized') %}
                                            （可查看所有人作品）
                                            {% else %}
                                            （仅可查看自己作品）
                                            {% endif %}
                                        </small>
                                    </div>
                                </div>
                            </div>
                            {% else %}
                            <!-- 未登录用户显示 -->
                            <div id="notLoggedInSection">
                                <div class="row align-items-center">
                                    <div class="col-md-8">
                                        <h5 class="mb-0">请登录以使用AI绘图功能</h5>
                                        <small class="text-muted">新用户注册即送10积分，每次生成消耗1积分</small>
                                    </div>
                                    <div class="col-md-4 text-end">
                                        <a href="/login" class="btn btn-primary btn-sm me-2">登录</a>
                                        <a href="/login" class="btn btn-outline-primary btn-sm">注册</a>
                                    </div>
                                </div>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- 左侧控制面板 -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        提示词
                    </div>
                    <div class="card-body">
                        <div class="d-flex mb-2">
                            <textarea id="promptInput" class="form-control" rows="5" placeholder="输入提示词..."></textarea>
                            <button id="randomTagBtn" class="btn btn-outline-secondary ms-2">从随机词库导入</button>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        反向提示词
                    </div>
                    <div class="card-body">
                        <textarea id="negativePromptInput" class="form-control" rows="3">lowres, {bad}, error, fewer, extra, missing, worst quality, jpeg artifacts, bad quality, watermark, unfinished, displeasing, chromatic aberration, signature, extra digits, artistic error, username, scan, [abstract]</textarea>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        参数设置
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-6">
                                <label for="widthInput" class="form-label">宽度:</label>
                                <input type="number" id="widthInput" class="form-control" value="832" min="512" max="1024" step="64">
                            </div>
                            <div class="col-6">
                                <label for="heightInput" class="form-label">高度:</label>
                                <input type="number" id="heightInput" class="form-control" value="1216" min="512" max="1024" step="64">
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-6">
                                <label for="stepsInput" class="form-label">步数:</label>
                                <input type="number" id="stepsInput" class="form-control" value="28" min="20" max="50">
                            </div>
                            <div class="col-6">
                                <label for="cfgInput" class="form-label">CFG:</label>
                                <input type="number" id="cfgInput" class="form-control" value="7" min="1" max="30">
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="modelSelect" class="form-label">模型:</label>
                            <select id="modelSelect" class="form-select">
                                {% for model in models %}
                                <option value="{{ loop.index0 }}" data-proxy="{{ model.proxy_url }}" data-type="{{ model.type }}">{{ model.display_name }}</option>
                                {% endfor %}
                            </select>
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="appendDefaultPromptCheck" checked>
                                <label class="form-check-label" for="appendDefaultPromptCheck">
                                    附加推荐质量提示词
                                </label>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="enableHrCheck" checked>
                                    <label class="form-check-label" for="enableHrCheck">
                                        使用负面词条
                                    </label>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="randomSeedCheck" checked>
                                    <label class="form-check-label" for="randomSeedCheck">
                                        随机种子
                                    </label>
                                </div>
                            </div>
                        </div>



                        <div class="mb-3">
                            <label for="seedInput" class="form-label">种子:</label>
                            <input type="number" id="seedInput" class="form-control" value="0" min="-1" max="2147483647">
                        </div>

                        <!-- 视频生成专用参数 -->
                        <div id="videoParams" style="display: none;">
                            <div class="mb-3">
                                <label for="imagePathInput" class="form-label">输入图片URL:</label>
                                <div class="input-group">
                                    <input type="text" id="imagePathInput" class="form-control" value="" placeholder="输入图片URL...">
                                    <button class="btn btn-outline-secondary" type="button" id="uploadImageBtn">本地上传</button>
                                    <button class="btn btn-outline-secondary" type="button" id="pasteImageBtn">粘贴图片</button>
                                    <input type="file" id="imageFileInput" style="display: none;" accept="image/*">
                                </div>
                                <div class="mt-2">
                                    <img id="imagePreview" class="img-thumbnail" style="max-height: 150px; max-width: 150px; display: none;" alt="图片预览">
                                    <span id="imageStatusText"></span>
                                </div>
                                <div id="directUrlResult" class="mt-2" style="display: none;">
                                    <div class="alert alert-success">
                                        <strong>直连URL已生成：</strong>
                                        <div class="input-group mt-2">
                                            <input type="text" id="directUrlText" class="form-control" readonly>
                                            <button class="btn btn-outline-secondary" type="button" id="copyDirectUrlBtn">复制</button>
                                            <button class="btn btn-outline-secondary" type="button" id="useDirectUrlBtn">使用此URL</button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row mb-3" style="display: none;">
                                <div class="col-6">
                                    <label for="motionBucketInput" class="form-label">运动强度:</label>
                                    <input type="number" id="motionBucketInput" class="form-control" value="2" min="1" max="10">
                                </div>
                                <div class="col-6">
                                    <label for="condAugInput" class="form-label">条件增强:</label>
                                    <input type="number" id="condAugInput" class="form-control" value="1" min="0" max="5">
                                </div>
                            </div>
                        </div>



                        <div class="d-grid gap-2">
                            <button id="generateBtn" class="btn btn-primary">生成图像</button>
                            <button id="clearLogBtn" class="btn btn-outline-danger">清除日志</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧预览区域 -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <span id="previewTitle">图片预览</span>
                    </div>
                    <div class="card-body">
                        <div class="preview-container">
                            <div id="loadingIndicator" class="loading">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                <p class="mt-2" id="loadingText">正在生成图片，请稍候...</p>
                            </div>
                            <img id="previewImage" class="preview-image" alt="生成的图片将在此显示">
                            <video id="previewVideo" class="preview-image" controls style="display: none;" alt="生成的视频将在此显示">
                                您的浏览器不支持视频播放。
                            </video>
                            <div id="previewPlaceholder">生成内容后将在此显示</div>
                        </div>
                        <div class="d-grid mt-3">
                            <button id="openUrlBtn" class="btn btn-secondary" disabled>在浏览器中查看原文件</button>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        日志
                    </div>
                    <div class="card-body">
                        <div id="logContainer" class="log-container">
                            <div id="logText">
                                <p>这是梦羽的绘图工具服务端版,后端使用H200作图</p>
                                <p>您对测试生成的内容负全部责任。</p>
                                <p>系统已配置代理池，所有请求将通过管理员配置的代理池进行处理。</p>
                                <p>有限的技术支持和反馈问题提交可以联系QQ:1031029814</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>





    <!-- 创建兑换码模态框 -->
    <div class="modal fade" id="createCodeModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-plus-circle me-2"></i>创建兑换码
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="createCodeForm">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label for="newCodeType" class="form-label">
                                    <i class="fas fa-tag me-1"></i>兑换码类型
                                </label>
                                <select id="newCodeType" class="form-select" required>
                                    <option value="one_time">一次性兑换码</option>
                                    <option value="activity">活动兑换码</option>
                                </select>
                                <div class="form-text">
                                    一次性：使用一次后失效；活动码：每个用户只能使用一次
                                </div>
                            </div>
                            <div class="col-md-6">
                                <label for="newCodePoints" class="form-label">
                                    <i class="fas fa-coins me-1"></i>积分数量
                                </label>
                                <input type="number" id="newCodePoints" class="form-control" value="10" min="1" max="10000" required>
                                <div class="form-text">每个兑换码可获得的积分数量</div>
                            </div>
                            <div class="col-md-6">
                                <label for="newCodeCount" class="form-label">
                                    <i class="fas fa-hashtag me-1"></i>生成数量
                                </label>
                                <input type="number" id="newCodeCount" class="form-control" value="1" min="1" max="1000" required>
                                <div class="form-text">一次生成的兑换码数量</div>
                            </div>
                            <div class="col-md-6">
                                <label for="newCodeExpireDays" class="form-label">
                                    <i class="fas fa-calendar-alt me-1"></i>有效天数
                                </label>
                                <input type="number" id="newCodeExpireDays" class="form-control" value="30" min="1" max="365" required>
                                <div class="form-text">兑换码的有效期（天）</div>
                            </div>
                            <div class="col-12">
                                <label for="newCodeDescription" class="form-label">
                                    <i class="fas fa-comment me-1"></i>描述信息
                                </label>
                                <input type="text" id="newCodeDescription" class="form-control" placeholder="输入兑换码的用途或活动名称">
                                <div class="form-text">可选，用于标识兑换码的用途</div>
                            </div>
                        </div>

                        <!-- 快速模板 -->
                        <div class="mt-4">
                            <h6 class="mb-3">
                                <i class="fas fa-magic me-2"></i>快速模板
                            </h6>
                            <div class="row g-2">
                                <div class="col-md-3">
                                    <button type="button" class="btn btn-outline-primary btn-sm w-100" onclick="applyCodeTemplate('welcome')">
                                        新用户奖励<br><small>10积分·30天</small>
                                    </button>
                                </div>
                                <div class="col-md-3">
                                    <button type="button" class="btn btn-outline-success btn-sm w-100" onclick="applyCodeTemplate('activity')">
                                        活动奖励<br><small>50积分·7天</small>
                                    </button>
                                </div>
                                <div class="col-md-3">
                                    <button type="button" class="btn btn-outline-warning btn-sm w-100" onclick="applyCodeTemplate('vip')">
                                        VIP福利<br><small>100积分·60天</small>
                                    </button>
                                </div>
                                <div class="col-md-3">
                                    <button type="button" class="btn btn-outline-info btn-sm w-100" onclick="applyCodeTemplate('test')">
                                        测试用码<br><small>1积分·1天</small>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 预览信息 -->
                        <div class="mt-4">
                            <div class="alert alert-info">
                                <h6 class="alert-heading">
                                    <i class="fas fa-info-circle me-2"></i>生成预览
                                </h6>
                                <p class="mb-1">
                                    将生成 <strong id="previewCount">1</strong> 个
                                    <strong id="previewType">一次性</strong>兑换码
                                </p>
                                <p class="mb-1">
                                    每个兑换码价值 <strong id="previewPoints">10</strong> 积分
                                </p>
                                <p class="mb-1">
                                    有效期至 <strong id="previewExpireDate">-</strong>
                                </p>
                                <p class="mb-0">
                                    总价值：<strong id="previewTotalPoints">10</strong> 积分
                                </p>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>取消
                    </button>
                    <button type="button" id="confirmCreateCodeBtn" class="btn btn-primary">
                        <i class="fas fa-magic me-1"></i>生成兑换码
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 批量操作模态框 -->
    <div class="modal fade" id="batchOperationModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-tasks me-2"></i>批量操作
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>已选择 <strong id="selectedCodesCount">0</strong> 个兑换码</p>
                    <div class="d-grid gap-2">
                        <button type="button" class="btn btn-success" id="batchActivateBtn">
                            <i class="fas fa-check-circle me-2"></i>批量启用
                        </button>
                        <button type="button" class="btn btn-warning" id="batchDeactivateBtn">
                            <i class="fas fa-pause-circle me-2"></i>批量禁用
                        </button>
                        <button type="button" class="btn btn-danger" id="batchDeleteBtn">
                            <i class="fas fa-trash me-2"></i>批量删除
                        </button>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 统一使用代理池，无需检查配置状态

            // 当前用户状态
            let currentUser = {% if current_user %}{{ current_user | tojson }}{% else %}null{% endif %};

            // 如果用户已登录，获取未读消息数量
            if (currentUser) {
                //loadUnreadMessageCount();
            }

            // 元素引用
            const promptInput = document.getElementById('promptInput');
            const negativePromptInput = document.getElementById('negativePromptInput');
            const widthInput = document.getElementById('widthInput');
            const heightInput = document.getElementById('heightInput');
            const stepsInput = document.getElementById('stepsInput');
            const cfgInput = document.getElementById('cfgInput');
            const modelSelect = document.getElementById('modelSelect');
            const appendDefaultPromptCheck = document.getElementById('appendDefaultPromptCheck');
            const enableHrCheck = document.getElementById('enableHrCheck');
            const randomSeedCheck = document.getElementById('randomSeedCheck');

            const seedInput = document.getElementById('seedInput');
            const generateBtn = document.getElementById('generateBtn');
            const openUrlBtn = document.getElementById('openUrlBtn');
            const clearLogBtn = document.getElementById('clearLogBtn');
            const randomTagBtn = document.getElementById('randomTagBtn');
            const logText = document.getElementById('logText');
            const previewImage = document.getElementById('previewImage');
            const previewVideo = document.getElementById('previewVideo');
            const previewPlaceholder = document.getElementById('previewPlaceholder');
            const loadingIndicator = document.getElementById('loadingIndicator');
            const loadingText = document.getElementById('loadingText');
            const previewTitle = document.getElementById('previewTitle');

            const videoParams = document.getElementById('videoParams');
            const imagePathInput = document.getElementById('imagePathInput');
            const motionBucketInput = document.getElementById('motionBucketInput');
            const condAugInput = document.getElementById('condAugInput');
            const imagePreview = document.getElementById('imagePreview');
            const imageStatusText = document.getElementById('imageStatusText');
            const uploadImageBtn = document.getElementById('uploadImageBtn');
            const pasteImageBtn = document.getElementById('pasteImageBtn');
            const imageFileInput = document.getElementById('imageFileInput');
            const directUrlResult = document.getElementById('directUrlResult');
            const directUrlText = document.getElementById('directUrlText');
            const copyDirectUrlBtn = document.getElementById('copyDirectUrlBtn');
            const useDirectUrlBtn = document.getElementById('useDirectUrlBtn');

            // 当前图片信息
            let currentImageUrl = null;
            let originalImageUrl = null;

            // 用户认证相关元素
            const logoutBtn = document.getElementById('logoutBtn');
            const userPointsSpan = document.getElementById('userPoints');

            // 添加日志函数
            function log(message) {
                const p = document.createElement('p');
                p.textContent = message;
                logText.appendChild(p);
                const logContainer = document.getElementById('logContainer');
                logContainer.scrollTop = logContainer.scrollHeight;
            }

            // 更新用户界面
            function updateUserInterface(user) {
                currentUser = user;
                if (user) {
                    // 更新积分显示
                    if (userPointsSpan) {
                        userPointsSpan.textContent = user.points;
                    }

                    // 显示已登录状态
                    const loggedInSection = document.getElementById('loggedInSection');
                    const notLoggedInSection = document.getElementById('notLoggedInSection');
                    if (loggedInSection && notLoggedInSection) {
                        loggedInSection.style.display = 'block';
                        notLoggedInSection.style.display = 'none';

                        // 更新用户名显示
                        const usernameSpan = loggedInSection.querySelector('h5');
                        if (usernameSpan) {
                            usernameSpan.textContent = `欢迎，${user.username}！`;
                        }

                        // 显示兑换码按钮区域
                        const redemptionSection = document.getElementById('redemptionSection');
                        if (redemptionSection) {
                            redemptionSection.style.display = 'block';
                        }

                        // 检查签到状态
                        checkCheckinStatus();
                    }
                } else {
                    // 显示未登录状态
                    const loggedInSection = document.getElementById('loggedInSection');
                    const notLoggedInSection = document.getElementById('notLoggedInSection');
                    if (loggedInSection && notLoggedInSection) {
                        loggedInSection.style.display = 'none';
                        notLoggedInSection.style.display = 'block';
                    }

                    // 隐藏兑换码按钮区域而不是移除
                    const redemptionSection = document.getElementById('redemptionSection');
                    if (redemptionSection) {
                        redemptionSection.style.display = 'none';
                        console.log('已隐藏兑换码按钮');
                    }
                }
            }

            // 检查签到状态
            function checkCheckinStatus() {
                const checkinBtn = document.getElementById('checkinBtn');
                if (!checkinBtn || !currentUser) return;

                fetch('/checkin_status', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.has_checked_in) {
                        // 今日已签到，更新按钮状态
                        checkinBtn.innerHTML = '<i class="fas fa-check me-1"></i>今日已签到';
                        checkinBtn.classList.remove('btn-outline-primary');
                        checkinBtn.classList.add('btn-success');
                        checkinBtn.disabled = true;
                    }
                })
                .catch(error => {
                    console.error('检查签到状态错误:', error);
                });
            }

            // 检查用户是否已登录
            function checkLoginStatus() {
                if (!currentUser) {
                    log('请先登录后再使用生成功能');
                    // 跳转到登录页面
                    window.location.href = '/login';
                    return false;
                }

                if (currentUser.points <= 0) {
                    log('积分不足，请联系管理员充值');
                    return false;
                }

                return true;
            }

            // 自动上传图片到图床并获取直连URL
            function autoUploadImageToHost(imageDataUrl) {
                log('正在自动上传图片获取直连URL...');

                // 将图片转换为Blob（不压缩）
                function dataURLtoBlob(dataURL) {
                    const arr = dataURL.split(',');
                    const mime = arr[0].match(/:(.*?);/)[1];
                    const bstr = atob(arr[1]);
                    let n = bstr.length;
                    const u8arr = new Uint8Array(n);
                    while (n--) {
                        u8arr[n] = bstr.charCodeAt(n);
                    }
                    return new Blob([u8arr], { type: mime });
                }

                // 直接转换图片为Blob（不压缩）
                const imageBlob = dataURLtoBlob(imageDataUrl);
                const sizeMB = imageBlob.size / (1024 * 1024);
                log(`图片大小: ${sizeMB.toFixed(2)}MB`);

                // 准备表单数据 - 直接使用原始图片数据
                const formData = new FormData();
                formData.append('file', imageBlob, `miaomiao_${Date.now()}.png`);

                // 直接发送到ggexacg-imger-55.deno.dev
                return fetch('https://steam.exacg.cc/image/upload.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP错误! 状态: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    log('收到响应:', JSON.stringify(data));

                    // 解析steam.exacg.cc的响应格式
                    if (data && data.success === true && data.url) {
                        log('图片上传成功！');
                        log(`文件名：${data.filename}`);
                        log(`文件大小：${data.size} 字节`);
                        log(`文件类型：${data.type}`);
                        log(`上传时间：${new Date(data.timestamp * 1000).toLocaleString()}`);
                        log(`直连URL：${data.url}`);

                        // 自动填入URL到输入框
                        if (imagePathInput) {
                            imagePathInput.value = data.url;
                        }

                        return data.url;
                    } else {
                        throw new Error(`图片上传失败：${data.message || '未知错误'}`);
                    }
                })
                .catch(error => {
                    log(`上传过程中发生错误：${error.message}`);
                    console.error('上传错误详情:', error);
                    throw error;
                });
            }

            // 清除日志
            clearLogBtn.addEventListener('click', function() {
                logText.innerHTML = '';
            });



            // 退出登录
            if (logoutBtn) {
                logoutBtn.addEventListener('click', function(e) {
                    e.preventDefault(); // 阻止默认行为
                    e.stopPropagation(); // 阻止事件冒泡

                    // 禁用按钮防止重复点击
                    logoutBtn.disabled = true;
                    logoutBtn.textContent = '退出中...';

                    fetch('/logout', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            log(data.message);
                            updateUserInterface(null);
                            // 刷新页面以更新服务器端状态
                            setTimeout(() => {
                                window.location.reload();
                            }, 1000);
                        } else {
                            log(`退出登录失败: ${data.message || '未知错误'}`);
                            // 恢复按钮状态
                            logoutBtn.disabled = false;
                            logoutBtn.textContent = '退出登录';
                        }
                    })
                    .catch(error => {
                        log(`退出登录时发生错误: ${error}`);
                        // 恢复按钮状态
                        logoutBtn.disabled = false;
                        logoutBtn.textContent = '退出登录';
                    });
                });
            }

            // 每日签到
            const checkinBtn = document.getElementById('checkinBtn');
            if (checkinBtn) {
                checkinBtn.addEventListener('click', function(e) {
                    e.preventDefault(); // 阻止默认的链接跳转

                    if (!currentUser) {
                        log('请先登录后再签到');
                        return;
                    }

                    // 禁用按钮防止重复点击
                    checkinBtn.disabled = true;
                    const originalText = checkinBtn.innerHTML;
                    checkinBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>签到中...';

                    fetch('/daily_checkin', {
                        method: 'GET',
                        headers: {
                            'Content-Type': 'application/json'
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // 更新用户积分显示
                            if (userPointsSpan) {
                                userPointsSpan.textContent = data.new_points;
                            }
                            currentUser.points = data.new_points;

                            log(`${data.message} 当前积分：${data.new_points}`);

                            // 签到成功后更新按钮状态
                            checkinBtn.innerHTML = '<i class="fas fa-check me-1"></i>今日已签到';
                            checkinBtn.classList.remove('btn-outline-primary');
                            checkinBtn.classList.add('btn-success');
                        } else {
                            log(data.message);
                            // 恢复按钮状态
                            checkinBtn.innerHTML = originalText;
                            checkinBtn.disabled = false;
                        }
                    })
                    .catch(error => {
                        console.error('签到错误:', error);
                        log('签到失败，请重试');
                        // 恢复按钮状态
                        checkinBtn.innerHTML = originalText;
                        checkinBtn.disabled = false;
                    });
                });
            }




            // 获取随机标签
            randomTagBtn.addEventListener('click', function() {
                fetch('/random_tag')
                    .then(response => response.json())
                    .then(data => {
                        promptInput.value = data.tag;
                        log(`已随机导入提示词: ${data.tag}`);
                    })
                    .catch(error => {
                        log(`导入随机提示词时发生错误: ${error}`);
                    });
            });

            // 检查视频模型队列状态的函数
            function checkVideoModelQueueStatus(modelIndex) {
                fetch(`/video_queue_status?model_index=${modelIndex}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.error) {
                            log(`队列状态检查错误: ${data.error}`);
                        } else if (data.is_processing) {
                            log(`⚠️ 模型 "${data.model_name}" 当前正在排队中，已等待 ${data.elapsed_time} 秒`);
                            log(`💡 建议：${data.message}`);
                        } else {
                            log(`✅ 模型 "${data.model_name}" 队列空闲，可以开始生成`);
                        }
                    })
                    .catch(error => {
                        log(`检查队列状态时发生错误: ${error}`);
                    });
            }

            // 模型变更处理
            modelSelect.addEventListener('change', function() {
                const selectedOption = modelSelect.options[modelSelect.selectedIndex];
                const hasProxyUrl = selectedOption.dataset.proxy !== '';
                const modelType = selectedOption.dataset.type;
                const modelIndex = modelSelect.value;

                // 根据模型类型显示/隐藏相应的参数
                if (modelType === 'video') {
                    videoParams.style.display = 'block';
                    previewTitle.textContent = '视频预览';
                    generateBtn.textContent = '生成视频';
                    // 为视频模型设置默认参数
                    widthInput.value = 512;;
                    heightInput.value = 896;
                    stepsInput.value = 4;
                    //视频模式下高度宽度步数cfg禁用
                    widthInput.disabled = true;
                    heightInput.disabled = true;
                    stepsInput.disabled = true;
                    cfgInput.disabled = true;
                    promptInput.placeholder = '输入视频描述提示词，例如：make this image come alive, cinematic motion, smooth animation';

                    // 检查视频模型队列状态
                    log(`🔍 正在检查模型 "${selectedOption.textContent}" 的队列状态...`);
                    checkVideoModelQueueStatus(modelIndex);
                    negativePromptInput.value = 'Bright tones, overexposed, static, blurred details, subtitles, style, works, paintings, images, static, overall gray, worst quality, low quality, JPEG compression residue, ugly, incomplete, extra fingers, poorly drawn hands, poorly drawn faces, deformed, disfigured, misshapen limbs, fused fingers, still picture, messy background, three legs, many people in the background, walking backwards, watermark, text, signature';
                } else {
                    videoParams.style.display = 'none';
                    //图片模式下高度宽度步数cfg重新启用
                    widthInput.disabled = false;
                    heightInput.disabled = false;
                    stepsInput.disabled = false;
                    cfgInput.disabled = false;
                    previewTitle.textContent = '图片预览';
                    generateBtn.textContent = '生成图像';
                    // 为图像模型设置默认参数
                    widthInput.value = 832;
                    heightInput.value = 1216;
                    stepsInput.value = 28;
                    promptInput.placeholder = '输入提示词...';
                    negativePromptInput.value = 'lowres, {bad}, error, fewer, extra, missing, worst quality, jpeg artifacts, bad quality, watermark, unfinished, displeasing, chromatic aberration, signature, extra digits, artistic error, username, scan, [abstract]';
                }

                // 统一使用管理员配置的代理池，无需用户选择
                log(`已选择 ${selectedOption.text}，将使用管理员配置的代理池`);
            });

            // 生成内容（图像或视频）
            generateBtn.addEventListener('click', function() {
                // 检查登录状态
                if (!checkLoginStatus()) {
                    return;
                }

                const prompt = promptInput.value.trim();
                const selectedOption = modelSelect.options[modelSelect.selectedIndex];
                const modelType = selectedOption.dataset.type;

                if (!prompt) {
                    log("错误：提示词不能为空!");
                    return;
                }

                // 禁用生成按钮
                generateBtn.disabled = true;
                openUrlBtn.disabled = true;

                // 显示加载指示器
                previewPlaceholder.style.display = 'none';
                previewImage.style.display = 'none';
                previewVideo.style.display = 'none';
                loadingIndicator.style.display = 'block';

                // 根据模型类型设置加载文本
                if (modelType === 'video') {
                    loadingText.textContent = '正在生成视频，请稍候...（视频生成需要较长时间，约1-3分钟）';
                } else {
                    loadingText.textContent = '正在生成图片，请稍候...';
                }

                // 准备表单数据
                const formData = new FormData();
                formData.append('prompt', prompt);
                formData.append('negative_prompt', negativePromptInput.value.trim());
                formData.append('width', widthInput.value);
                formData.append('height', heightInput.value);
                formData.append('steps', stepsInput.value);
                formData.append('seed', seedInput.value);
                // 统一使用管理员配置的代理池，无需传递use_proxy_address参数

                // 根据模型类型添加不同的参数
                let endpoint = '/generate';
                if (modelType === 'video') {
                    // 视频生成参数
                    // 优先使用直连URL，如果没有URL则使用base64数据
                    let imageSource = '';
                    if (imagePathInput.value.trim() !== '') {
                        imageSource = imagePathInput.value.trim(); // 优先使用URL
                        log("使用直连URL...");
                    } else if (imagePreview.style.display !== 'none') {
                        imageSource = imagePreview.src; // 备用：使用预览图片的base64数据
                        log("使用上传/粘贴的图片base64数据...");
                    }

                    if (!imageSource) {
                        log("错误：未提供图片源！请上传图片或提供图片URL。");
                        generateBtn.disabled = false;
                        openUrlBtn.disabled = false;
                        loadingIndicator.style.display = 'none';
                        previewPlaceholder.style.display = 'block';
                        return;
                    }

                    formData.append('image_source', imageSource);
                    formData.append('motion_bucket_id', motionBucketInput.value);
                    formData.append('cond_aug', condAugInput.value);
                    formData.append('model_index', modelSelect.value);  // 添加模型索引
                    endpoint = '/generate_video';
                    log(`开始生成视频，使用模型: ${selectedOption.textContent}...`);
                } else {
                    // 图像生成参数
                    formData.append('cfg', cfgInput.value);
                    formData.append('enable_hr', enableHrCheck.checked);
                    formData.append('restore_faces', randomSeedCheck.checked);
                    formData.append('model_index', modelSelect.value);
                    formData.append('append_default_prompt', appendDefaultPromptCheck.checked);
                    log("开始生成图像...");
                }

                // 发送请求
                fetch(endpoint, {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        log(data.message);

                        // 更新用户积分显示
                        if (data.user_points !== undefined && userPointsSpan) {
                            userPointsSpan.textContent = data.user_points;
                            currentUser.points = data.user_points;
                        }

                        // 显示代理日志信息
                        if (data.proxy_logs && Array.isArray(data.proxy_logs)) {
                            data.proxy_logs.forEach(proxy_log => {
                                log(proxy_log);
                            });
                        }

                        loadingIndicator.style.display = 'none';

                        if (modelType === 'video') {
                            // 显示视频
                            previewVideo.src = data.video_url;
                            previewVideo.style.display = 'block';

                            // 保存原始URL
                            currentImageUrl = data.video_url;
                            originalImageUrl = data.video_url;

                            // 启用按钮
                            openUrlBtn.disabled = false;

                            log("成功获取视频，可以在浏览器中查看原视频");
                        } else {
                            // 显示图片
                            previewImage.src = data.image_url;
                            previewImage.style.display = 'block';

                            // 保存原始URL
                            currentImageUrl = data.image_url;
                            originalImageUrl = data.original_url || data.image_url;

                            // 启用按钮
                            openUrlBtn.disabled = false;

                            log("成功获取图片，可以在浏览器中查看原图");
                        }
                    } else {
                        // 检查是否是队列繁忙
                        if (data.queue_busy && modelType === 'video') {
                            const modelName = data.model_name || selectedOption.textContent;
                            log(`队列繁忙: ${data.message}`);
                            log(`模型 ${modelName} 正在被其他用户使用，将在10秒后自动重试...`);

                            // 显示代理日志信息
                            if (data.proxy_logs && Array.isArray(data.proxy_logs)) {
                                data.proxy_logs.forEach(proxy_log => {
                                    log(proxy_log);
                                });
                            }

                            // 10秒后自动重试
                            setTimeout(() => {
                                log(`正在重新尝试生成视频，模型: ${modelName}...`);
                                generateBtn.click();
                            }, 10000);

                            return;
                        }

                        log(`生成失败: ${data.message}`);

                        // 显示代理日志信息
                        if (data.proxy_logs && Array.isArray(data.proxy_logs)) {
                            data.proxy_logs.forEach(proxy_log => {
                                log(proxy_log);
                            });
                        }

                        loadingIndicator.style.display = 'none';
                        previewPlaceholder.style.display = 'block';
                        previewPlaceholder.textContent = "生成失败";
                    }
                })
                .catch(error => {
                    log(`请求过程中发生错误: ${error}`);
                    loadingIndicator.style.display = 'none';
                    previewPlaceholder.style.display = 'block';
                    previewPlaceholder.textContent = "生成失败";
                })
                .finally(() => {
                    // 重新启用生成按钮
                    generateBtn.disabled = false;
                });
            });

            // 在浏览器中打开原图
            openUrlBtn.addEventListener('click', function() {
                if (originalImageUrl) {
                    window.open(originalImageUrl, '_blank');
                }
            });

            // 随机种子复选框
            randomSeedCheck.addEventListener('change', function() {
                if (randomSeedCheck.checked) {
                    seedInput.value = 0;
                }
            });

            // 初始模型检查
            const initialSelectedOption = modelSelect.options[modelSelect.selectedIndex];
            const initialHasProxyUrl = initialSelectedOption.dataset.proxy !== '';
            const initialModelType = initialSelectedOption.dataset.type;

            // 初始化界面状态
            if (initialModelType === 'video') {
                videoParams.style.display = 'block';
                previewTitle.textContent = '视频预览';
                generateBtn.textContent = '生成视频';

                // 初始化时也检查视频模型队列状态
                const initialModelIndex = modelSelect.value;
                log(`🔍 正在检查初始模型 "${initialSelectedOption.textContent}" 的队列状态...`);
                checkVideoModelQueueStatus(initialModelIndex);
            } else {
                videoParams.style.display = 'none';
                previewTitle.textContent = '图片预览';
                generateBtn.textContent = '生成图像';
            }

            // 统一使用管理员配置的代理池，无需用户选择

            // 本地上传功能
            if (uploadImageBtn && imageFileInput) {
                uploadImageBtn.addEventListener('click', function() {
                    imageFileInput.click();
                });

                imageFileInput.addEventListener('change', function() {
                    const file = this.files[0];
                    if (file) {
                        const reader = new FileReader();
                        reader.onload = function(e) {
                            if (imagePreview) {
                                imagePreview.src = e.target.result;
                                imagePreview.style.display = 'block';
                            }
                            if (imageStatusText) {
                                imageStatusText.textContent = '图片已上传，正在获取直连URL...';
                            }
                            if (directUrlResult) {
                                directUrlResult.style.display = 'none';
                            }

                            // 自动上传到图床获取直连URL
                            autoUploadImageToHost(e.target.result)
                                .then(url => {
                                    if (imageStatusText) {
                                        imageStatusText.textContent = '图片已上传并获取直连URL';
                                    }
                                })
                                .catch(error => {
                                    if (imageStatusText) {
                                        imageStatusText.textContent = '图片已上传，但获取直连URL失败';
                                    }
                                });
                        };
                        reader.readAsDataURL(file);
                    }
                });
            }

            // 粘贴图片功能
            if (pasteImageBtn) {
                pasteImageBtn.addEventListener('click', function() {
                    navigator.clipboard.read()
                        .then(clipboardItems => {
                            for (const clipboardItem of clipboardItems) {
                                if (clipboardItem.types.includes('image/png') ||
                                    clipboardItem.types.includes('image/jpeg') ||
                                    clipboardItem.types.includes('image/jpg') ||
                                    clipboardItem.types.includes('image/gif')) {

                                    const imageType = clipboardItem.types.find(type =>
                                        type === 'image/png' || type === 'image/jpeg' ||
                                        type === 'image/jpg' || type === 'image/gif'
                                    );

                                    clipboardItem.getType(imageType)
                                        .then(blob => {
                                            const reader = new FileReader();
                                            reader.onload = function(e) {
                                                if (imagePreview) {
                                                    imagePreview.src = e.target.result;
                                                    imagePreview.style.display = 'block';
                                                }
                                                if (imageStatusText) {
                                                    imageStatusText.textContent = '已从剪贴板粘贴图片，正在获取直连URL...';
                                                }
                                                if (directUrlResult) {
                                                    directUrlResult.style.display = 'none';
                                                }

                                                // 自动上传到图床获取直连URL
                                                autoUploadImageToHost(e.target.result)
                                                    .then(url => {
                                                        if (imageStatusText) {
                                                            imageStatusText.textContent = '已从剪贴板粘贴图片并获取直连URL';
                                                        }
                                                    })
                                                    .catch(error => {
                                                        if (imageStatusText) {
                                                            imageStatusText.textContent = '已从剪贴板粘贴图片，但获取直连URL失败';
                                                        }
                                                    });
                                            };
                                            reader.readAsDataURL(blob);
                                        });
                                }
                            }
                        })
                        .catch(err => {
                            console.error('无法访问剪贴板:', err);
                            if (imageStatusText) {
                                imageStatusText.textContent = '粘贴失败，请检查浏览器权限';
                            }
                        });
                });
            }

            // 也支持全局粘贴事件
            document.addEventListener('paste', function(e) {
                // 只有当焦点在图片相关区域时才处理粘贴事件
                const activeElement = document.activeElement;
                if ((imagePathInput && activeElement === imagePathInput) ||
                    (uploadImageBtn && activeElement === uploadImageBtn) ||
                    (pasteImageBtn && activeElement === pasteImageBtn)) {

                    const clipboardData = e.clipboardData;
                    if (clipboardData.items) {
                        for (let i = 0; i < clipboardData.items.length; i++) {
                            if (clipboardData.items[i].type.indexOf('image') !== -1) {
                                const blob = clipboardData.items[i].getAsFile();
                                const reader = new FileReader();
                                reader.onload = function(e) {
                                    if (imagePreview) {
                                        imagePreview.src = e.target.result;
                                        imagePreview.style.display = 'block';
                                    }
                                    if (imageStatusText) {
                                        imageStatusText.textContent = '已从剪贴板粘贴图片，正在获取直连URL...';
                                    }
                                    if (directUrlResult) {
                                        directUrlResult.style.display = 'none';
                                    }

                                    // 自动上传到图床获取直连URL
                                    autoUploadImageToHost(e.target.result)
                                        .then(url => {
                                            if (imageStatusText) {
                                                imageStatusText.textContent = '已从剪贴板粘贴图片并获取直连URL';
                                            }
                                        })
                                        .catch(error => {
                                            if (imageStatusText) {
                                                imageStatusText.textContent = '已从剪贴板粘贴图片，但获取直连URL失败';
                                            }
                                        });
                                };
                                reader.readAsDataURL(blob);
                                e.preventDefault();
                                break;
                            }
                        }
                    }
                }
            });

            // 更新视频生成表单提交
            const videoForm = document.getElementById('videoForm');
            if (videoForm) {
                videoForm.addEventListener('submit', function(e) {
                    e.preventDefault();

                const formData = new FormData();

                // 优先使用直连URL，如果没有URL则使用base64数据
                let imageSource = '';
                if (imagePathInput && imagePathInput.value.trim() !== '') {
                    imageSource = imagePathInput.value.trim(); // 优先使用URL
                } else if (imagePreview && imagePreview.style.display !== 'none') {
                    imageSource = imagePreview.src; // 备用：使用预览图片的base64数据
                } else {
                    alert('请提供图片URL或上传图片');
                    return;
                }

                formData.append('image_source', imageSource);
                formData.append('prompt', document.getElementById('videoPromptInput').value);
                formData.append('width', document.getElementById('videoWidthInput').value);
                formData.append('height', document.getElementById('videoHeightInput').value);
                formData.append('negative_prompt', document.getElementById('videoNegativePromptInput').value);
                formData.append('motion_bucket_id', document.getElementById('motionBucketInput').value);
                formData.append('cond_aug', document.getElementById('condAugInput').value);
                formData.append('steps', document.getElementById('videoStepsInput').value);
                formData.append('seed', document.getElementById('videoSeedInput').value);
                // 统一使用管理员配置的代理池，无需传递use_proxy_address参数
                formData.append('model_index', modelSelect.value);  // 添加模型索引

                // 显示加载状态
                document.getElementById('videoGenerateBtn').disabled = true;
                document.getElementById('videoGenerateBtn').innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 生成中...';

                // 清空之前的日志
                document.getElementById('videoProxyLogs').innerHTML = '';

                // 发送请求
                fetch('/generate_video', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    document.getElementById('videoGenerateBtn').disabled = false;
                    document.getElementById('videoGenerateBtn').textContent = '生成视频';

                    if (data.success) {
                        // 显示结果
                        document.getElementById('videoResult').innerHTML = `
                            <div class="alert alert-success">生成成功！</div>
                            <div class="mb-3">
                                <video controls class="img-fluid" style="max-width: 100%;">
                                    <source src="${data.video_url}" type="video/mp4">
                                    您的浏览器不支持视频标签。
                                </video>
                            </div>
                            <div class="mb-3">
                                <a href="${data.video_url}" class="btn btn-primary" target="_blank">在新窗口打开</a>
                            </div>
                        `;
                    } else {
                        // 检查是否是队列繁忙
                        if (data.queue_busy) {
                            const modelName = data.model_name || '当前选择的模型';
                            document.getElementById('videoResult').innerHTML = `
                                <div class="alert alert-warning">队列繁忙：${data.message}</div>
                                <div class="alert alert-info">模型 ${modelName} 正在被其他用户使用，将在10秒后自动重试...</div>
                            `;

                            // 10秒后自动重试
                            setTimeout(() => {
                                document.getElementById('videoResult').innerHTML = `
                                    <div class="alert alert-info">正在重新尝试生成视频，模型: ${modelName}...</div>
                                `;
                                // 重新提交表单
                                videoForm.dispatchEvent(new Event('submit'));
                            }, 10000);

                            return;
                        }

                        document.getElementById('videoResult').innerHTML = `
                            <div class="alert alert-danger">生成失败：${data.message}</div>
                        `;
                    }

                    // 显示代理日志
                    if (data.proxy_logs && data.proxy_logs.length > 0) {
                        const logsHtml = data.proxy_logs.map(log => `<div>${log}</div>`).join('');
                        document.getElementById('videoProxyLogs').innerHTML = logsHtml;
                    }
                })
                .catch(error => {
                    document.getElementById('videoGenerateBtn').disabled = false;
                    document.getElementById('videoGenerateBtn').textContent = '生成视频';
                    document.getElementById('videoResult').innerHTML = `
                        <div class="alert alert-danger">请求错误：${error}</div>
                    `;
                });
                });
            }



            // 复制直连URL
            if (copyDirectUrlBtn) {
                copyDirectUrlBtn.addEventListener('click', function() {
                    if (directUrlText) {
                        directUrlText.select();
                        document.execCommand('copy');
                        log('直连URL已复制到剪贴板');
                    }
                });
            }

            // 使用直连URL
            if (useDirectUrlBtn) {
                useDirectUrlBtn.addEventListener('click', function() {
                    if (directUrlText && imagePathInput) {
                        imagePathInput.value = directUrlText.value;
                        if (imagePreview) {
                            imagePreview.style.display = 'none';
                        }
                        if (directUrlResult) {
                            directUrlResult.style.display = 'none';
                        }
                        if (imageStatusText) {
                            imageStatusText.textContent = '已使用直连URL';
                        }
                        log('已将直连URL设置为图片源');
                    }
                });
            }



            // ==================== 兑换码管理相关函数 ====================

            // 显示兑换码统计
            window.displayRedemptionStats = function(stats) {
                // 更新统计卡片
                document.getElementById('totalCodesCount').textContent = stats.total_codes || 0;
                document.getElementById('activeCodesCount').textContent = stats.active_codes || 0;
                document.getElementById('usedCodesCount').textContent = stats.total_codes_used || 0;
                document.getElementById('totalPointsDistributed').textContent = stats.total_points_distributed || 0;
            };

            // 显示最近使用记录
            window.displayRecentRedemptions = function(records) {
                const container = document.getElementById('recentRedemptionsContainer');

                if (records.length === 0) {
                    container.innerHTML = '<div class="text-center p-4"><p class="text-muted mb-0">暂无使用记录</p></div>';
                    return;
                }

                let html = '';
                records.slice(0, 20).forEach((record, index) => {
                    const date = new Date(record.used_at).toLocaleString();
                    const typeText = record.code_type === 'one_time' ? '一次性' : '活动码';
                    const typeClass = record.code_type === 'one_time' ? 'bg-primary' : 'bg-success';

                    html += `
                        <div class="d-flex align-items-center p-3 ${index < records.length - 1 ? 'border-bottom' : ''}">
                            <div class="flex-shrink-0">
                                <div class="bg-light rounded-circle p-2">
                                    <i class="fas fa-user text-muted"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <h6 class="mb-1">${record.username}</h6>
                                        <p class="mb-1 text-muted small">兑换码: <code>${record.code}</code></p>
                                        <small class="text-muted">${date}</small>
                                    </div>
                                    <div class="text-end">
                                        <span class="badge ${typeClass} mb-1">${typeText}</span>
                                        <div class="text-success fw-bold">+${record.points} 积分</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                });

                container.innerHTML = html;
            };

            // 显示兑换码列表
            window.displayRedemptionCodesTable = function(codes) {
                const container = document.getElementById('redemptionCodesContainer');
                const countBadge = document.getElementById('codesCountBadge');

                // 更新数量徽章
                countBadge.textContent = codes.length;

                if (codes.length === 0) {
                    container.innerHTML = '<div class="text-center p-4"><p class="text-muted mb-0">暂无兑换码</p></div>';
                    return;
                }

                let html = '';
                codes.forEach((code, index) => {
                    const expireDate = new Date(code.expire_at);
                    const isExpired = new Date() > expireDate;
                    const createDate = new Date(code.created_at);

                    // 状态判断
                    let statusText, statusClass, statusIcon;
                    if (isExpired) {
                        statusText = '已过期';
                        statusClass = 'bg-danger';
                        statusIcon = 'fas fa-times-circle';
                    } else if (!code.is_active) {
                        statusText = '已禁用';
                        statusClass = 'bg-warning';
                        statusIcon = 'fas fa-pause-circle';
                    } else if (code.type === 'one_time' && code.used_count > 0) {
                        statusText = '已使用';
                        statusClass = 'bg-secondary';
                        statusIcon = 'fas fa-check-circle';
                    } else {
                        statusText = '有效';
                        statusClass = 'bg-success';
                        statusIcon = 'fas fa-check-circle';
                    }

                    const typeText = code.type === 'one_time' ? '一次性' : '活动码';
                    const typeClass = code.type === 'one_time' ? 'bg-primary' : 'bg-info';

                    html += `
                        <div class="d-flex align-items-center p-3 ${index < codes.length - 1 ? 'border-bottom' : ''}">
                            <div class="form-check me-3">
                                <input class="form-check-input code-checkbox" type="checkbox" value="${code.code}" id="code_${code.code}">
                            </div>
                            <div class="flex-grow-1">
                                <div class="row align-items-center">
                                    <div class="col-md-3">
                                        <div class="d-flex align-items-center">
                                            <div class="me-3">
                                                <i class="fas fa-ticket-alt text-primary fs-5"></i>
                                            </div>
                                            <div>
                                                <h6 class="mb-1"><code class="text-dark">${code.code}</code></h6>
                                                <small class="text-muted">创建于 ${createDate.toLocaleDateString()}</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <span class="badge ${typeClass}">${typeText}</span>
                                        <div class="small text-muted mt-1">${code.points} 积分</div>
                                    </div>
                                    <div class="col-md-2">
                                        <span class="badge ${statusClass}">
                                            <i class="${statusIcon} me-1"></i>${statusText}
                                        </span>
                                        <div class="small text-muted mt-1">使用 ${code.used_count} 次</div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="small">
                                            <div class="text-muted">过期时间</div>
                                            <div class="${isExpired ? 'text-danger' : 'text-dark'}">${expireDate.toLocaleDateString()}</div>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="small text-muted" title="${code.description || '无描述'}">
                                            ${code.description ? (code.description.length > 20 ? code.description.substring(0, 20) + '...' : code.description) : '无描述'}
                                        </div>
                                    </div>
                                    <div class="col-md-1">
                                        <div class="dropdown">
                                            <button class="btn btn-outline-secondary btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                                <i class="fas fa-ellipsis-v"></i>
                                            </button>
                                            <ul class="dropdown-menu">
                                                ${!isExpired ? `
                                                    <li>
                                                        <a class="dropdown-item" href="#" onclick="toggleRedemptionCode('${code.code}', '${code.is_active ? 'deactivate' : 'activate'}')">
                                                            <i class="fas fa-${code.is_active ? 'pause' : 'play'} me-2"></i>
                                                            ${code.is_active ? '禁用' : '启用'}
                                                        </a>
                                                    </li>
                                                ` : ''}
                                                <li>
                                                    <a class="dropdown-item" href="#" onclick="copyCodeToClipboard('${code.code}')">
                                                        <i class="fas fa-copy me-2"></i>复制兑换码
                                                    </a>
                                                </li>
                                                <li><hr class="dropdown-divider"></li>
                                                <li>
                                                    <a class="dropdown-item text-danger" href="#" onclick="deleteRedemptionCode('${code.code}')">
                                                        <i class="fas fa-trash me-2"></i>删除
                                                    </a>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                });

                container.innerHTML = html;
            };

            // 加载兑换码数据
            window.loadRedemptionData = function() {
                // 加载兑换码统计
                fetch('/admin/redemption_statistics')
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            displayRedemptionStats(data.statistics);
                        } else {
                            console.error('加载统计数据失败:', data.message);
                            // 显示错误状态
                            document.getElementById('totalCodesCount').textContent = '-';
                            document.getElementById('activeCodesCount').textContent = '-';
                            document.getElementById('usedCodesCount').textContent = '-';
                            document.getElementById('totalPointsDistributed').textContent = '-';
                        }
                    })
                    .catch(error => {
                        console.error('加载统计数据失败:', error);
                        // 显示错误状态
                        document.getElementById('totalCodesCount').textContent = '-';
                        document.getElementById('activeCodesCount').textContent = '-';
                        document.getElementById('usedCodesCount').textContent = '-';
                        document.getElementById('totalPointsDistributed').textContent = '-';
                    });

                // 加载兑换码列表
                fetch('/admin/redemption_codes')
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            displayRedemptionCodesTable(data.codes);
                        } else {
                            const container = document.getElementById('redemptionCodesContainer');
                            if (container) {
                                container.innerHTML = `<div class="alert alert-danger m-3">${data.message}</div>`;
                            }
                        }
                    })
                    .catch(error => {
                        const container = document.getElementById('redemptionCodesContainer');
                        if (container) {
                            container.innerHTML = `<div class="alert alert-danger m-3">加载兑换码失败: ${error}</div>`;
                        }
                    });

                // 加载使用记录
                fetch('/admin/redemption_usage_records')
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            displayRecentRedemptions(data.records);
                        } else {
                            const container = document.getElementById('recentRedemptionsContainer');
                            if (container) {
                                container.innerHTML = `<div class="alert alert-danger m-3">${data.message}</div>`;
                            }
                        }
                    })
                    .catch(error => {
                        const container = document.getElementById('recentRedemptionsContainer');
                        if (container) {
                            container.innerHTML = `<div class="alert alert-danger m-3">加载使用记录失败: ${error}</div>`;
                        }
                    });
            };




            // 显示待审核用户表格
            function displayPendingUsersTable(pendingUsers) {
                if (pendingUsers.length === 0) {
                    document.getElementById('pendingUsersTable').innerHTML = '<div class="alert alert-info">暂无待审核用户</div>';
                    return;
                }

                let html = `
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>用户名</th>
                                    <th>邮箱</th>
                                    <th>申请理由</th>
                                    <th>注册时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                `;

                pendingUsers.forEach(user => {
                    const reason = user.registration_reason || '-';
                    const reasonDisplay = reason.length > 30 ? reason.substring(0, 30) + '...' : reason;
                    html += `
                        <tr>
                            <td>${user.username}</td>
                            <td>${user.email || '-'}</td>
                            <td>
                                <span title="${reason}" data-bs-toggle="tooltip">${reasonDisplay}</span>
                            </td>
                            <td>${user.created_at ? new Date(user.created_at).toLocaleDateString() : '-'}</td>
                            <td>
                                <button class="btn btn-success btn-sm me-1" onclick="approveUser('${user.username}')">通过</button>
                                <button class="btn btn-danger btn-sm" onclick="rejectUser('${user.username}')">拒绝</button>
                            </td>
                        </tr>
                    `;
                });

                html += `
                            </tbody>
                        </table>
                    </div>
                `;

                document.getElementById('pendingUsersTable').innerHTML = html;

                // 初始化 Bootstrap tooltips
                const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
                tooltipTriggerList.map(function (tooltipTriggerEl) {
                    return new bootstrap.Tooltip(tooltipTriggerEl);
                });
            }

            // 显示统计信息
            function displayStatistics(stats) {
                const html = `
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-body">
                                    <h6 class="card-title">系统统计</h6>
                                    <p>总用户数: ${stats.total_users}</p>
                                    <p>总交易数: ${stats.system_stats.total_transactions}</p>
                                    <p>总发放积分: ${stats.system_stats.total_points_issued}</p>
                                    <p>总消耗积分: ${stats.system_stats.total_points_consumed}</p>
                                    <p>总生成次数: ${stats.system_stats.total_generations}</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-body">
                                    <h6 class="card-title">生成排行榜</h6>
                                    ${stats.top_users.map((user, index) =>
                                        `<p>${index + 1}. ${user.username}: ${user.generations}次</p>`
                                    ).join('')}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="mt-3">
                        <h6>最近活动</h6>
                        <div class="table-responsive" style="max-height: 300px; overflow-y: auto;">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>用户</th>
                                        <th>积分变化</th>
                                        <th>类型</th>
                                        <th>描述</th>
                                        <th>时间</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${stats.recent_activities.map(activity => `
                                        <tr>
                                            <td>${activity.username}</td>
                                            <td class="${activity.points_change > 0 ? 'text-success' : 'text-danger'}">
                                                ${activity.points_change > 0 ? '+' : ''}${activity.points_change}
                                            </td>
                                            <td>${activity.type}</td>
                                            <td>${activity.description}</td>
                                            <td>${new Date(activity.timestamp).toLocaleString()}</td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        </div>
                    </div>
                `;

                document.getElementById('statisticsContent').innerHTML = html;
            }

            // 积分充值功能
            const chargeBtn = document.getElementById('chargeBtn');
            if (chargeBtn) {
                chargeBtn.addEventListener('click', function() {
                    const username = document.getElementById('chargeUsername').value.trim();
                    const points = parseInt(document.getElementById('chargePoints').value);
                    const reason = document.getElementById('chargeReason').value.trim() || '管理员充值';

                    if (!username || !points || points <= 0) {
                        alert('请填写正确的用户名和积分数');
                        return;
                    }

                    fetch('/admin/add_points', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            username: username,
                            points: points,
                            reason: reason
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            alert(data.message);
                            // 清空表单
                            document.getElementById('chargeUsername').value = '';
                            document.getElementById('chargePoints').value = '';
                            document.getElementById('chargeReason').value = '';
                            // 重新加载数据
                            loadAdminData();
                        } else {
                            alert(data.message);
                        }
                    })
                    .catch(error => {
                        alert(`充值失败: ${error}`);
                    });
                });
            }

            // 保存代理池设置
            const saveProxySettingsBtn = document.getElementById('saveProxySettingsBtn');
            if (saveProxySettingsBtn) {
                saveProxySettingsBtn.addEventListener('click', function() {
                    const globalProxyApiUrl = document.getElementById('globalProxyApiUrl').value.trim();

                    fetch('/admin/settings', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            global_proxy_api_url: globalProxyApiUrl
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            alert('代理池设置保存成功！');
                            // 刷新页面以更新全局代理池状态
                            location.reload();
                        } else {
                            alert(data.message);
                        }
                    })
                    .catch(error => {
                        alert(`保存失败: ${error}`);
                    });
                });
            }

            // 保存积分设置
            const savePointsSettingsBtn = document.getElementById('savePointsSettingsBtn');
            if (savePointsSettingsBtn) {
                savePointsSettingsBtn.addEventListener('click', function() {
                    const imageGenerationCost = parseInt(document.getElementById('imageGenerationCost').value);
                    const videoGenerationCost = parseInt(document.getElementById('videoGenerationCost').value);

                    if (imageGenerationCost < 1 || videoGenerationCost < 1) {
                        alert('积分消耗必须大于0');
                        return;
                    }

                    fetch('/admin/settings', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            image_generation_cost: imageGenerationCost,
                            video_generation_cost: videoGenerationCost
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            alert('积分设置保存成功！');
                        } else {
                            alert(data.message);
                        }
                    })
                    .catch(error => {
                        alert(`保存失败: ${error}`);
                    });
                });
            }

            // 保存注册设置
            const saveRegistrationSettingsBtn = document.getElementById('saveRegistrationSettingsBtn');
            if (saveRegistrationSettingsBtn) {
                saveRegistrationSettingsBtn.addEventListener('click', function() {
                    const requireRegistrationApproval = document.getElementById('requireRegistrationApproval').checked;

                    fetch('/admin/settings', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            require_registration_approval: requireRegistrationApproval
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            alert('注册设置保存成功！');
                        } else {
                            alert(data.message);
                        }
                    })
                    .catch(error => {
                        alert(`保存失败: ${error}`);
                    });
                });
            }

            // 刷新待审核用户按钮
            const refreshPendingUsersBtn = document.getElementById('refreshPendingUsersBtn');
            if (refreshPendingUsersBtn) {
                refreshPendingUsersBtn.addEventListener('click', function() {
                    fetch('/admin/pending_users')
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                displayPendingUsersTable(data.pending_users);
                            } else {
                                document.getElementById('pendingUsersTable').innerHTML = `<div class="alert alert-danger">${data.message}</div>`;
                            }
                        })
                        .catch(error => {
                            document.getElementById('pendingUsersTable').innerHTML = `<div class="alert alert-danger">加载待审核用户失败: ${error}</div>`;
                        });
                });
            }
        });

        // 审核通过用户
        function approveUser(username) {
            if (confirm(`确定要通过用户 "${username}" 的审核吗？`)) {
                fetch('/admin/approve_user', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: username
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert(data.message);
                        // 刷新待审核用户列表
                        document.getElementById('refreshPendingUsersBtn').click();
                    } else {
                        alert(data.message);
                    }
                })
                .catch(error => {
                    alert(`操作失败: ${error}`);
                });
            }
        }

        // 拒绝用户
        function rejectUser(username) {
            // 先确认是否要拒绝并删除用户
            if (!confirm(`⚠️ 警告：拒绝用户 "${username}" 将会彻底删除该账户！\n\n此操作不可恢复，确定要继续吗？`)) {
                return;
            }

            const reason = prompt(`请输入拒绝用户 "${username}" 的原因（可选）:`);
            if (reason !== null) { // 用户没有取消
                fetch('/admin/reject_user', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: username,
                        reason: reason
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert(data.message);
                        // 刷新待审核用户列表
                        document.getElementById('refreshPendingUsersBtn').click();
                    } else {
                        alert(data.message);
                    }
                })
                .catch(error => {
                    alert(`操作失败: ${error}`);
                });
            }





            // 复制兑换码到剪贴板
            window.copyCodeToClipboard = function(code) {
                navigator.clipboard.writeText(code).then(function() {
                    // 显示成功提示
                    const toast = document.createElement('div');
                    toast.className = 'toast align-items-center text-white bg-success border-0 position-fixed';
                    toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999;';
                    toast.innerHTML = `
                        <div class="d-flex">
                            <div class="toast-body">
                                <i class="fas fa-check me-2"></i>兑换码已复制到剪贴板
                            </div>
                            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                        </div>
                    `;
                    document.body.appendChild(toast);
                    const bsToast = new bootstrap.Toast(toast);
                    bsToast.show();
                    toast.addEventListener('hidden.bs.toast', () => {
                        document.body.removeChild(toast);
                    });
                }).catch(function() {
                    alert('复制失败，请手动复制：' + code);
                });
            };

            // 应用兑换码模板
            window.applyCodeTemplate = function(templateType) {
                const templates = {
                    welcome: { type: 'one_time', points: 10, count: 1, days: 30, desc: '新用户欢迎奖励' },
                    activity: { type: 'activity', points: 50, count: 1, days: 7, desc: '活动奖励' },
                    vip: { type: 'activity', points: 100, count: 1, days: 60, desc: 'VIP用户福利' },
                    test: { type: 'one_time', points: 1, count: 1, days: 1, desc: '测试用兑换码' }
                };

                const template = templates[templateType];
                if (template) {
                    document.getElementById('newCodeType').value = template.type;
                    document.getElementById('newCodePoints').value = template.points;
                    document.getElementById('newCodeCount').value = template.count;
                    document.getElementById('newCodeExpireDays').value = template.days;
                    document.getElementById('newCodeDescription').value = template.desc;
                    updateCreateCodePreview();
                }
            };

            // 更新创建兑换码预览
            window.updateCreateCodePreview = function() {
                const type = document.getElementById('newCodeType').value;
                const points = parseInt(document.getElementById('newCodePoints').value) || 0;
                const count = parseInt(document.getElementById('newCodeCount').value) || 0;
                const days = parseInt(document.getElementById('newCodeExpireDays').value) || 0;

                const typeText = type === 'one_time' ? '一次性' : '活动';
                const expireDate = new Date();
                expireDate.setDate(expireDate.getDate() + days);

                document.getElementById('previewType').textContent = typeText;
                document.getElementById('previewCount').textContent = count;
                document.getElementById('previewPoints').textContent = points;
                document.getElementById('previewExpireDate').textContent = expireDate.toLocaleDateString();
                document.getElementById('previewTotalPoints').textContent = points * count;
            };

            // ==================== 新的兑换码管理事件监听器 ====================

            // 初始化兑换码管理功能
            function initRedemptionCodeManagement() {
                // 创建兑换码按钮
                const createCodeBtn = document.getElementById('createCodeBtn');
                const createCodeModal = new bootstrap.Modal(document.getElementById('createCodeModal'));

                if (createCodeBtn) {
                    // 移除可能存在的旧事件监听器
                    createCodeBtn.replaceWith(createCodeBtn.cloneNode(true));
                    const newCreateCodeBtn = document.getElementById('createCodeBtn');

                    newCreateCodeBtn.addEventListener('click', function(e) {
                        e.preventDefault();
                        console.log('创建兑换码按钮被点击');
                        try {
                            createCodeModal.show();
                            updateCreateCodePreview();
                        } catch (error) {
                            console.error('显示模态框失败:', error);
                            alert('显示创建兑换码对话框失败: ' + error.message);
                        }
                    });
                    console.log('创建兑换码按钮事件绑定成功');
                } else {
                    console.error('创建兑换码按钮未找到');
                }

                // 绑定其他事件...
                bindOtherRedemptionEvents();
            }

            // 绑定其他兑换码相关事件
            function bindOtherRedemptionEvents() {
                // 创建兑换码表单输入变化时更新预览
                ['newCodeType', 'newCodePoints', 'newCodeCount', 'newCodeExpireDays'].forEach(id => {
                    const element = document.getElementById(id);
                    if (element) {
                        element.addEventListener('input', updateCreateCodePreview);
                        element.addEventListener('change', updateCreateCodePreview);
                    }
                });
            }

            // 使用事件委托处理创建兑换码按钮点击
            document.addEventListener('click', function(e) {
                if (e.target && e.target.id === 'createCodeBtn') {
                    e.preventDefault();
                    console.log('通过事件委托捕获到创建兑换码按钮点击');

                    const modalElement = document.getElementById('createCodeModal');
                    if (modalElement) {
                        try {
                            const createCodeModal = new bootstrap.Modal(modalElement);
                            createCodeModal.show();
                            updateCreateCodePreview();
                            console.log('模态框显示成功');
                        } catch (error) {
                            console.error('显示模态框失败:', error);
                            alert('显示创建兑换码对话框失败: ' + error.message);
                        }
                    } else {
                        console.error('模态框元素未找到');
                        alert('模态框元素未找到');
                    }
                }
            });

            // 监听兑换码管理标签页激活事件
            const redemptionTabTrigger = document.querySelector('a[href="#redemptionTab"]');
            if (redemptionTabTrigger) {
                redemptionTabTrigger.addEventListener('shown.bs.tab', function() {
                    console.log('兑换码管理标签页已激活，重新初始化');
                    setTimeout(initRedemptionCodeManagement, 100);
                });
            }

            // 如果兑换码标签页已经是激活状态，直接初始化
            const redemptionTab = document.getElementById('redemptionTab');
            if (redemptionTab && redemptionTab.classList.contains('active')) {
                initRedemptionCodeManagement();
            }

            // 确认创建兑换码
            const confirmCreateCodeBtn = document.getElementById('confirmCreateCodeBtn');
            if (confirmCreateCodeBtn) {
                confirmCreateCodeBtn.addEventListener('click', function() {
                    const codeType = document.getElementById('newCodeType').value;
                    const points = parseInt(document.getElementById('newCodePoints').value);
                    const count = parseInt(document.getElementById('newCodeCount').value);
                    const expireDays = parseInt(document.getElementById('newCodeExpireDays').value);
                    const description = document.getElementById('newCodeDescription').value.trim();

                    if (!points || points <= 0) {
                        alert('请输入有效的积分数');
                        return;
                    }

                    if (!count || count <= 0 || count > 1000) {
                        alert('生成数量必须在1-1000之间');
                        return;
                    }

                    if (!expireDays || expireDays <= 0 || expireDays > 365) {
                        alert('有效天数必须在1-365之间');
                        return;
                    }

                    this.disabled = true;
                    this.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>生成中...';

                    fetch('/admin/create_redemption_codes', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            type: codeType,
                            points: points,
                            count: count,
                            expire_days: expireDays,
                            description: description
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        this.disabled = false;
                        this.innerHTML = '<i class="fas fa-magic me-1"></i>生成兑换码';

                        if (data.success) {
                            // 显示成功消息和生成的兑换码
                            const codesText = data.codes.join('\n');
                            const message = `${data.message}\n\n生成的兑换码:\n${codesText}`;

                            // 创建一个更好的显示对话框
                            const modalHtml = `
                                <div class="modal fade" id="codesResultModal" tabindex="-1">
                                    <div class="modal-dialog modal-lg">
                                        <div class="modal-content">
                                            <div class="modal-header bg-success text-white">
                                                <h5 class="modal-title">
                                                    <i class="fas fa-check-circle me-2"></i>兑换码生成成功
                                                </h5>
                                                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                                            </div>
                                            <div class="modal-body">
                                                <p class="mb-3">${data.message}</p>
                                                <div class="alert alert-info">
                                                    <h6 class="alert-heading">生成的兑换码：</h6>
                                                    <textarea class="form-control" rows="6" readonly>${codesText}</textarea>
                                                    <div class="mt-2">
                                                        <button class="btn btn-outline-primary btn-sm" onclick="navigator.clipboard.writeText('${codesText}')">
                                                            <i class="fas fa-copy me-1"></i>复制全部
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            `;

                            // 添加模态框到页面
                            document.body.insertAdjacentHTML('beforeend', modalHtml);
                            const resultModal = new bootstrap.Modal(document.getElementById('codesResultModal'));
                            resultModal.show();

                            // 模态框关闭后移除
                            document.getElementById('codesResultModal').addEventListener('hidden.bs.modal', function() {
                                this.remove();
                            });

                            // 关闭创建模态框
                            createCodeModal.hide();

                            // 清空表单
                            document.getElementById('newCodeType').value = 'one_time';
                            document.getElementById('newCodePoints').value = '10';
                            document.getElementById('newCodeCount').value = '1';
                            document.getElementById('newCodeExpireDays').value = '30';
                            document.getElementById('newCodeDescription').value = '';
                            updateCreateCodePreview();

                            // 重新加载数据
                            loadRedemptionData();
                        } else {
                            alert(data.message);
                        }
                    })
                    .catch(error => {
                        this.disabled = false;
                        this.innerHTML = '<i class="fas fa-magic me-1"></i>生成兑换码';
                        alert(`生成失败: ${error}`);
                    });
                });
            }

            // 筛选和搜索功能
            const applyFiltersBtn = document.getElementById('applyFiltersBtn');
            const clearFiltersBtn = document.getElementById('clearFiltersBtn');

            if (applyFiltersBtn) {
                applyFiltersBtn.addEventListener('click', function() {
                    // 这里可以添加筛选逻辑
                    loadRedemptionData();
                });
            }

            if (clearFiltersBtn) {
                clearFiltersBtn.addEventListener('click', function() {
                    document.getElementById('searchCodeInput').value = '';
                    document.getElementById('filterCodeType').value = '';
                    document.getElementById('filterCodeStatus').value = '';
                    document.getElementById('filterPointsRange').value = '';
                    loadRedemptionData();
                });
            }

            // 全选/取消全选
            const selectAllCodesBtn = document.getElementById('selectAllCodesBtn');
            const deselectAllCodesBtn = document.getElementById('deselectAllCodesBtn');

            if (selectAllCodesBtn) {
                selectAllCodesBtn.addEventListener('click', function() {
                    document.querySelectorAll('.code-checkbox').forEach(cb => cb.checked = true);
                    updateSelectedCodesCount();
                });
            }

            if (deselectAllCodesBtn) {
                deselectAllCodesBtn.addEventListener('click', function() {
                    document.querySelectorAll('.code-checkbox').forEach(cb => cb.checked = false);
                    updateSelectedCodesCount();
                });
            }

            // 更新选中数量
            window.updateSelectedCodesCount = function() {
                const selectedCount = document.querySelectorAll('.code-checkbox:checked').length;
                const countElement = document.getElementById('selectedCodesCount');
                if (countElement) {
                    countElement.textContent = selectedCount;
                }
            };

            // 刷新按钮
            const refreshCodesBtn = document.getElementById('refreshCodesBtn');
            if (refreshCodesBtn) {
                refreshCodesBtn.addEventListener('click', function() {
                    this.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>刷新中...';
                    loadRedemptionData();
                    setTimeout(() => {
                        this.innerHTML = '<i class="fas fa-sync-alt me-1"></i>刷新';
                    }, 1000);
                });
            }

            // 批量操作功能
            const batchOperationBtn = document.getElementById('batchOperationBtn');
            const batchOperationModal = new bootstrap.Modal(document.getElementById('batchOperationModal'));

            if (batchOperationBtn) {
                batchOperationBtn.addEventListener('click', function() {
                    const selectedCodes = document.querySelectorAll('.code-checkbox:checked');
                    if (selectedCodes.length === 0) {
                        alert('请先选择要操作的兑换码');
                        return;
                    }
                    updateSelectedCodesCount();
                    batchOperationModal.show();
                });
            }

            // 批量启用
            const batchActivateBtn = document.getElementById('batchActivateBtn');
            if (batchActivateBtn) {
                batchActivateBtn.addEventListener('click', function() {
                    const selectedCodes = Array.from(document.querySelectorAll('.code-checkbox:checked')).map(cb => cb.value);
                    if (selectedCodes.length === 0) return;

                    if (!confirm(`确定要启用选中的 ${selectedCodes.length} 个兑换码吗？`)) return;

                    batchOperationCodes(selectedCodes, 'activate');
                });
            }

            // 批量禁用
            const batchDeactivateBtn = document.getElementById('batchDeactivateBtn');
            if (batchDeactivateBtn) {
                batchDeactivateBtn.addEventListener('click', function() {
                    const selectedCodes = Array.from(document.querySelectorAll('.code-checkbox:checked')).map(cb => cb.value);
                    if (selectedCodes.length === 0) return;

                    if (!confirm(`确定要禁用选中的 ${selectedCodes.length} 个兑换码吗？`)) return;

                    batchOperationCodes(selectedCodes, 'deactivate');
                });
            }

            // 批量删除
            const batchDeleteBtn = document.getElementById('batchDeleteBtn');
            if (batchDeleteBtn) {
                batchDeleteBtn.addEventListener('click', function() {
                    const selectedCodes = Array.from(document.querySelectorAll('.code-checkbox:checked')).map(cb => cb.value);
                    if (selectedCodes.length === 0) return;

                    if (!confirm(`确定要删除选中的 ${selectedCodes.length} 个兑换码吗？此操作不可恢复！`)) return;

                    batchOperationCodes(selectedCodes, 'delete');
                });
            }

            // 批量操作函数
            window.batchOperationCodes = function(codes, action) {
                const promises = codes.map(code => {
                    let url, method, body;

                    if (action === 'delete') {
                        url = '/admin/delete_redemption_code';
                        method = 'POST';
                        body = JSON.stringify({ code: code });
                    } else {
                        url = '/admin/toggle_redemption_code';
                        method = 'POST';
                        body = JSON.stringify({ code: code, action: action });
                    }

                    return fetch(url, {
                        method: method,
                        headers: { 'Content-Type': 'application/json' },
                        body: body
                    }).then(response => response.json());
                });

                Promise.all(promises).then(results => {
                    const successCount = results.filter(r => r.success).length;
                    const failCount = results.length - successCount;

                    let message = `操作完成！成功: ${successCount}`;
                    if (failCount > 0) {
                        message += `，失败: ${failCount}`;
                    }

                    alert(message);
                    batchOperationModal.hide();
                    loadRedemptionData();
                }).catch(error => {
                    alert(`批量操作失败: ${error}`);
                });
            };

            // 导出功能
            const exportCodesBtn = document.getElementById('exportCodesBtn');
            if (exportCodesBtn) {
                exportCodesBtn.addEventListener('click', function() {
                    // 这里可以添加导出功能
                    alert('导出功能开发中...');
                });
            }

            // 生成兑换码（旧版本兼容）
            if (document.getElementById('generateCodesBtn')) {
                document.getElementById('generateCodesBtn').addEventListener('click', function() {
                    const codeType = document.getElementById('codeType').value;
                    const points = parseInt(document.getElementById('codePoints').value);
                    const count = parseInt(document.getElementById('codeCount').value);
                    const expireDays = parseInt(document.getElementById('codeExpireDays').value);
                    const description = document.getElementById('codeDescription').value.trim();

                    if (!points || points <= 0) {
                        alert('请输入有效的积分数');
                        return;
                    }

                    if (!count || count <= 0 || count > 1000) {
                        alert('生成数量必须在1-1000之间');
                        return;
                    }

                    if (!expireDays || expireDays <= 0 || expireDays > 365) {
                        alert('有效天数必须在1-365之间');
                        return;
                    }

                    this.disabled = true;
                    this.textContent = '生成中...';

                    fetch('/admin/create_redemption_codes', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            type: codeType,
                            points: points,
                            count: count,
                            expire_days: expireDays,
                            description: description
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        this.disabled = false;
                        this.textContent = '生成';

                        if (data.success) {
                            alert(`${data.message}\n\n生成的兑换码:\n${data.codes.join('\n')}`);
                            // 清空表单
                            document.getElementById('codePoints').value = '10';
                            document.getElementById('codeCount').value = '1';
                            document.getElementById('codeExpireDays').value = '30';
                            document.getElementById('codeDescription').value = '';
                            // 重新加载数据
                            loadRedemptionData();
                        } else {
                            alert(data.message);
                        }
                    })
                    .catch(error => {
                        this.disabled = false;
                        this.textContent = '生成';
                        alert(`生成失败: ${error}`);
                    });
                });
            }

            // 刷新兑换码数据
            if (document.getElementById('refreshRedemptionCodesBtn')) {
                document.getElementById('refreshRedemptionCodesBtn').addEventListener('click', function() {
                    loadRedemptionData();
                });
            }

            // 切换兑换码状态
            window.toggleRedemptionCode = function(code, action) {
                if (!confirm(`确定要${action === 'activate' ? '启用' : '禁用'}这个兑换码吗？`)) {
                    return;
                }

                fetch('/admin/toggle_redemption_code', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        code: code,
                        action: action
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert(data.message);
                        loadRedemptionData();
                    } else {
                        alert(data.message);
                    }
                })
                .catch(error => {
                    alert(`操作失败: ${error}`);
                });
            };

            // 删除兑换码
            window.deleteRedemptionCode = function(code) {
                if (!confirm(`确定要删除兑换码 ${code} 吗？此操作不可恢复！`)) {
                    return;
                }

                fetch('/admin/delete_redemption_code', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        code: code
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert(data.message);
                        loadRedemptionData();
                    } else {
                        alert(data.message);
                    }
                })
                .catch(error => {
                    alert(`删除失败: ${error}`);
                });
            };

            // ==================== 用户兑换码功能 ====================
            // 兑换码功能已移至独立页面 /redeem

            // ==================== 站内信功能 ====================

            // 加载未读消息数量
            function loadUnreadMessageCount() {
                fetch('/api/messages/unread_count')
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            updateUnreadBadge(data.unread_count);
                        } else {
                            console.error('获取未读消息数量失败:', data.message);
                        }
                    })
                    .catch(error => {
                        console.error('获取未读消息数量出错:', error);
                    });
            }

            // 更新未读消息徽章
            function updateUnreadBadge(count) {
                const unreadBadge = document.getElementById('unreadBadge');
                const unreadCount = document.getElementById('unreadCount');

                if (count > 0) {
                    unreadCount.textContent = count > 99 ? '99+' : count;
                    unreadBadge.classList.remove('d-none');
                } else {
                    unreadBadge.classList.add('d-none');
                }
            }

            // 定期检查未读消息数量（每5分钟检查一次）
            if (currentUser) {
                setInterval(loadUnreadMessageCount, 5 * 60 * 1000);
            }
        }
    </script>
</body>
</html>
